import { AlertTriangle } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../@/components/ui/tabs';
import { RootState } from '../../../redux/store';
import { DomainListProperties } from '../../../types/cookie-consent-management';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import { get_domain_list } from '../../common/services/cookie-consent-management';
import { CookieDashboard } from './cookie-dashboard';
import { CookieDiscovery } from './cookie-discovery';
import { Analytics } from './cookies-analysis';

export default function CookieConsentDashboard() {
  const lastUpdatedDate = new Date();
  lastUpdatedDate.setDate(lastUpdatedDate.getDate() - 1);

  const [selectedDomainId, setSelectedDomainId] = useState<number>(0);
  const [selectedDomainName, setSelectedDomainName] = useState<string>('');

  const [domainList, setDomainList] = useState<DomainListProperties[]>([]);

  const { t } = useTranslation();

  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const getDomainName = (selectedDomainId: number) => {
    const domain = domainList.find((item: any) => item.id === selectedDomainId);
    setSelectedDomainName(domain ? domain.domain_name : '');
  };
  useEffect(() => {
    getDomainName(selectedDomainId);
  }, [selectedDomainId]);

  useEffect(() => {
    const fetchDomainList = async () => {
      try {
        const responseData = await get_domain_list(customer_id);
        setDomainList(responseData?.result?.rows);
      } catch (error) {
        console.error(error);
      }
    };

    fetchDomainList();
  }, [customer_id]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Cookie Consent Management</h1>
        </div>
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center justify-center gap-3 rounded-md bg-yellow-50 p-1 font-medium shadow-sm">
            <AlertTriangle className="size-6 text-yellow-600" />
            <div>
              <p className="text-sm text-yellow-700">
                The data displayed reflects information collected up to{' '}
                {convertDateToHumanView(lastUpdatedDate.toString())}
              </p>
            </div>
          </div>
          <div className="w-44">
            <Select
              value={selectedDomainId?.toString()}
              onValueChange={(value) => setSelectedDomainId(Number(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">{t('Cookies.All Domains')}</SelectItem>
                {domainList?.map((domain: DomainListProperties) => {
                  return (
                    <SelectItem value={String(domain?.id)} key={domain?.id}>
                      {domain?.domain_name}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Tabs defaultValue="dashboard" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          {/* <TabsTrigger value="builder">Banner Builder</TabsTrigger> */}
          {/* <TabsTrigger value="consent">Consent Management</TabsTrigger> */}
          <TabsTrigger value="discovery">Cookie Discovery</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <CookieDashboard
            selectedDomainId={selectedDomainId}
            selectedDomainName={selectedDomainName}
          />
        </TabsContent>

        <TabsContent value="discovery">
          <CookieDiscovery             selectedDomainId={selectedDomainId}
/>
        </TabsContent>

        <TabsContent value="analytics">
          <Analytics selectedDomainId={selectedDomainId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
