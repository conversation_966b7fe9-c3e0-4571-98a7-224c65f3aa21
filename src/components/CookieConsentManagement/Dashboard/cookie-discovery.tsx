import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import httpClient from '../../../api/httpClientNew';
import { RootState } from '../../../redux/store';
import { FETCH_COOKIES_BY_CATEGORY } from '../../common/api';
import { CookieDictionary } from '../CookieDictionary/cookie-dictionary';

export function CookieDiscovery({ selectedDomainId }: { selectedDomainId: number }) {
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );

  const { data: cookieCategoryData, isLoading: isCookieCategoryDataLoading } = useQuery({
    queryKey: ['cookie_category_data', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await httpClient.get(
        `${FETCH_COOKIES_BY_CATEGORY}?customer_id=${customer_id}&domain_id=${selectedDomainId}`
      );
      return response?.data?.data?.result;
    },
    enabled: !!customer_id && selectedDomainId >= 0,
  });

  console.log(cookieCategoryData,"cookieCategoryData")

  return (
    <div className="space-y-6">
      <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Necessary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">12</div>
            <p className="text-sm text-slate-600">Essential cookies</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">8</div>
            <p className="text-sm text-slate-600">Performance tracking</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Marketing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">15</div>
            <p className="text-sm text-slate-600">Advertising cookies</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Functional</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-600">5</div>
            <p className="text-sm text-slate-600">Feature enhancement</p>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Cookie Inventory</CardTitle>
          <CardDescription>All discovered cookies across your domains</CardDescription>
        </CardHeader>
        <CardContent>
          <CookieDictionary useForDashboard={true} />
        </CardContent>
      </Card>
    </div>
  );
}
